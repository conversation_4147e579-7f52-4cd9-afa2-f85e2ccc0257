'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState, Suspense } from 'react';

interface FormData {
  nickname: string;
  realName: string;
  gender: string;
  birthday: string;
  hometown: string;
  education: string;
  jobIntention: string;
  workExperience: string;
  japaneseLevel: string;
  email: string;
  password: string;
  phone: string;
  wechat: string;
}

function ConfirmPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [formData, setFormData] = useState<FormData | null>(null);

  useEffect(() => {
    const dataParam = searchParams.get('data');
    if (dataParam) {
      try {
        const decodedData = JSON.parse(decodeURIComponent(dataParam));
        setFormData(decodedData);
      } catch (error) {
        console.error('解析表单数据失败:', error);
        router.push('/consultation');
      }
    } else {
      router.push('/consultation');
    }
  }, [searchParams, router]);

  const handleConfirmSubmit = () => {
    // 这里可以添加实际的提交逻辑
    console.log('提交表单数据:', formData);
    alert('咨询信息提交成功！我们会尽快与您联系。');
    router.push('/');
  };

  const getDisplayValue = (key: string, value: string) => {
    if (!value) return '未填写';
    
    switch (key) {
      case 'gender':
        return value === 'male' ? '男' : '女';
      case 'education':
        const educationMap: Record<string, string> = {
          'below_secondary': '中专及以下',
          'college': '大专',
          'bachelor': '本科',
          'graduate': '研究生及以上'
        };
        return educationMap[value] || value;
      case 'jobIntention':
        return value === 'care' ? '介护' : '护士';
      case 'workExperience':
        const experienceMap: Record<string, string> = {
          'none': '无',
          '0-1': '0-1年',
          '1-2': '1-2年',
          '2-3': '2-3年',
          '3+': '3年以上'
        };
        return experienceMap[value] || value;
      case 'password':
        return '••••••••';
      default:
        return value;
    }
  };

  const getFieldLabel = (key: string, jobIntention: string) => {
    const labels: Record<string, string> = {
      nickname: '昵称',
      realName: '真实姓名',
      gender: '性别',
      birthday: '生日',
      hometown: '籍贯',
      education: '最终学历',
      jobIntention: '就业意向',
      workExperience: `${jobIntention === 'care' ? '介护' : '护士'}工作经验`,
      japaneseLevel: '日语等级',
      email: '邮箱',
      password: '密码',
      phone: '联系电话',
      wechat: '微信号'
    };
    return labels[key] || key;
  };

  if (!formData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-md mx-auto px-4 py-3 flex items-center justify-between">
          <button 
            onClick={() => router.back()}
            className="text-blue-600 hover:text-blue-700 font-medium"
          >
            ← 返回修改
          </button>
          <h1 className="text-lg font-bold text-gray-800">确认信息</h1>
          <div className="w-16"></div> {/* 占位符保持居中 */}
        </div>
      </header>

      {/* Content */}
      <main className="max-w-md mx-auto px-4 py-6">
        <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">请确认您的信息</h2>
          
          <div className="space-y-3">
            {Object.entries(formData).map(([key, value]) => (
              <div key={key} className="flex justify-between items-start py-2 border-b border-gray-100 last:border-b-0">
                <span className="text-sm font-medium text-gray-600 w-1/3">
                  {getFieldLabel(key, formData.jobIntention)}
                </span>
                <span className="text-sm text-gray-800 w-2/3 text-right">
                  {getDisplayValue(key, value)}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex items-start space-x-2">
            <span className="text-yellow-600 text-lg">⚠️</span>
            <div>
              <h3 className="font-semibold text-yellow-800 mb-1">提交须知</h3>
              <p className="text-yellow-700 text-sm">
                请仔细核对以上信息，提交后我们将根据您提供的信息为您匹配合适的工作机会。
                如有错误，请点击"返回修改"按钮进行修改。
              </p>
            </div>
          </div>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={() => router.back()}
            className="flex-1 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors"
          >
            返回修改
          </button>
          <button
            onClick={handleConfirmSubmit}
            className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
          >
            确认提交
          </button>
        </div>
      </main>
    </div>
  );
}

export default function ConfirmPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    }>
      <ConfirmPageContent />
    </Suspense>
  );
}
